/** Default theme settings */
export const themeSettings: App.Theme.ThemeSetting = {
  themeScheme: 'dark',
  grayscale: false,
  colourWeakness: false,
  recommendColor: false,
  themeColor: '#1890FF',
  otherColor: {
    info: '#1890FF',
    success: '#00D4AA',
    warning: '#FAAD14',
    error: '#FF4D4F'
  },
  isInfoFollowPrimary: true,
  resetCacheStrategy: 'close',
  layout: {
    mode: 'horizontal',
    scrollMode: 'content',
    reverseHorizontalMix: false
  },
  page: {
    animate: true,
    animateMode: 'fade-slide'
  },
  header: {
    height: 64,
    breadcrumb: {
      visible: false,
      showIcon: false
    },
    multilingual: {
      visible: false
    },
    globalSearch: {
      visible: false
    }
  },
  tab: {
    visible: false,
    cache: true,
    height: 44,
    mode: 'chrome'
  },
  fixedHeaderAndTab: true,
  sider: {
    inverted: true,
    width: 240,
    collapsedWidth: 64,
    mixWidth: 90,
    mixCollapsedWidth: 64,
    mixChildMenuWidth: 200
  },
  footer: {
    visible: false,
    fixed: false,
    height: 48,
    right: true
  },
  watermark: {
    visible: import.meta.env.VITE_WATERMARK === 'Y',
    text: 'RuoYi-Vue-Plus',
    enableUserName: false
  },
  table: {
    bordered: true,
    bottomBordered: true,
    singleColumn: false,
    singleLine: true,
    size: 'small',
    striped: false
  },
  tokens: {
    light: {
      colors: {
        container: 'rgb(255, 255, 255)',
        layout: 'rgb(247, 250, 252)',
        inverted: 'rgb(0, 20, 40)',
        'base-text': 'rgb(31, 31, 31)'
      },
      boxShadow: {
        header: '0 1px 2px rgb(0, 21, 41, 0.08)',
        sider: '2px 0 8px 0 rgb(29, 35, 41, 0.05)',
        tab: '0 1px 2px rgb(0, 21, 41, 0.08)'
      }
    },
    dark: {
      colors: {
        container: 'rgb(16, 24, 40)',
        layout: 'rgb(10, 18, 30)',
        inverted: 'rgb(24, 144, 255)',
        'base-text': 'rgb(255, 255, 255)'
      },
      boxShadow: {
        header: '0 2px 8px rgba(24, 144, 255, 0.15)',
        sider: '2px 0 8px rgba(24, 144, 255, 0.1)',
        tab: '0 1px 4px rgba(24, 144, 255, 0.12)'
      }
    }
  }
};

/**
 * Override theme settings
 *
 * If publish new version, use `overrideThemeSettings` to override certain theme settings
 */
export const overrideThemeSettings: Partial<App.Theme.ThemeSetting> = {};
