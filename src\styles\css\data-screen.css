/* 数据大屏专用样式 */

/* 全局背景和基础样式 */
html.dark {
  background: linear-gradient(135deg, 
    rgb(10, 18, 30) 0%, 
    rgb(16, 24, 40) 50%, 
    rgb(10, 18, 30) 100%
  ) !important;
}

body {
  background: transparent !important;
}

#app {
  background: transparent !important;
}

/* 数据大屏容器样式 */
.data-screen-container {
  background: transparent;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

/* 科技感网格背景 */
.data-screen-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(24, 144, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(24, 144, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  pointer-events: none;
  z-index: 0;
}

/* 发光效果 */
.glow-effect {
  box-shadow: 
    0 0 20px rgba(24, 144, 255, 0.3),
    inset 0 0 20px rgba(24, 144, 255, 0.1);
  border: 1px solid rgba(24, 144, 255, 0.3);
}

/* 数据卡片样式 */
.data-card {
  background: rgba(16, 24, 40, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(24, 144, 255, 0.2);
  border-radius: 8px;
  box-shadow: 
    0 4px 20px rgba(0, 0, 0, 0.3),
    0 0 40px rgba(24, 144, 255, 0.1);
  transition: all 0.3s ease;
}

.data-card:hover {
  border-color: rgba(24, 144, 255, 0.4);
  box-shadow: 
    0 8px 30px rgba(0, 0, 0, 0.4),
    0 0 60px rgba(24, 144, 255, 0.2);
  transform: translateY(-2px);
}

/* 标题样式 */
.data-screen-title {
  color: #ffffff;
  text-shadow: 0 0 10px rgba(24, 144, 255, 0.5);
  font-weight: 600;
}

.data-screen-subtitle {
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 0 5px rgba(24, 144, 255, 0.3);
}

/* 数值显示样式 */
.data-value {
  color: #1890FF;
  text-shadow: 0 0 15px rgba(24, 144, 255, 0.6);
  font-weight: bold;
  font-family: 'Courier New', monospace;
}

.data-value-large {
  font-size: 2.5rem;
  line-height: 1.2;
}

.data-value-medium {
  font-size: 1.8rem;
  line-height: 1.3;
}

.data-value-small {
  font-size: 1.2rem;
  line-height: 1.4;
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-online {
  background: #00D4AA;
  box-shadow: 0 0 10px rgba(0, 212, 170, 0.6);
}

.status-warning {
  background: #FAAD14;
  box-shadow: 0 0 10px rgba(250, 173, 20, 0.6);
}

.status-error {
  background: #FF4D4F;
  box-shadow: 0 0 10px rgba(255, 77, 79, 0.6);
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(24, 144, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(24, 144, 255, 0.5);
  }
}

.pulse-animation {
  animation: pulse 2s infinite;
}

.glow-animation {
  animation: glow 3s infinite;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(16, 24, 40, 0.5);
}

::-webkit-scrollbar-thumb {
  background: rgba(24, 144, 255, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(24, 144, 255, 0.7);
}

/* 响应式设计 */
@media (max-width: 1920px) {
  .data-value-large {
    font-size: 2rem;
  }
}

@media (max-width: 1366px) {
  .data-value-large {
    font-size: 1.8rem;
  }
  
  .data-card {
    padding: 16px;
  }
}

@media (max-width: 1024px) {
  .data-value-large {
    font-size: 1.5rem;
  }
  
  .data-screen-grid {
    background-size: 30px 30px;
  }
}

/* 图表容器样式 */
.chart-container {
  background: rgba(16, 24, 40, 0.6);
  border: 1px solid rgba(24, 144, 255, 0.2);
  border-radius: 8px;
  padding: 20px;
  backdrop-filter: blur(5px);
}

/* 数据表格样式 */
.data-table {
  background: rgba(16, 24, 40, 0.8);
  border: 1px solid rgba(24, 144, 255, 0.2);
}

.data-table th {
  background: rgba(24, 144, 255, 0.1);
  color: #ffffff;
  border-bottom: 1px solid rgba(24, 144, 255, 0.3);
}

.data-table td {
  color: rgba(255, 255, 255, 0.9);
  border-bottom: 1px solid rgba(24, 144, 255, 0.1);
}

.data-table tr:hover {
  background: rgba(24, 144, 255, 0.05);
}

/* 进度条样式 */
.progress-bar {
  background: rgba(16, 24, 40, 0.8);
  border: 1px solid rgba(24, 144, 255, 0.2);
  border-radius: 10px;
  overflow: hidden;
}

.progress-fill {
  background: linear-gradient(90deg, #1890FF, #00D4AA);
  height: 100%;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(24, 144, 255, 0.5);
  transition: width 0.3s ease;
}

/* 按钮样式增强 */
.data-screen-button {
  background: rgba(24, 144, 255, 0.2);
  border: 1px solid rgba(24, 144, 255, 0.4);
  color: #ffffff;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.data-screen-button:hover {
  background: rgba(24, 144, 255, 0.3);
  border-color: rgba(24, 144, 255, 0.6);
  box-shadow: 0 0 20px rgba(24, 144, 255, 0.3);
}

/* 输入框样式 */
.data-screen-input {
  background: rgba(16, 24, 40, 0.8);
  border: 1px solid rgba(24, 144, 255, 0.3);
  color: #ffffff;
  backdrop-filter: blur(5px);
}

.data-screen-input:focus {
  border-color: rgba(24, 144, 255, 0.6);
  box-shadow: 0 0 15px rgba(24, 144, 255, 0.2);
}

/* 工具提示样式 */
.data-tooltip {
  background: rgba(16, 24, 40, 0.95);
  border: 1px solid rgba(24, 144, 255, 0.3);
  color: #ffffff;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}
