/**
 * 数据大屏工具类
 * 提供数据大屏相关的工具函数和常量
 */

// 数据大屏主题色彩常量
export const DATA_SCREEN_COLORS = {
  primary: '#1890FF',
  success: '#00D4AA',
  warning: '#FAAD14',
  error: '#FF4D4F',
  background: {
    primary: 'rgb(10, 18, 30)',
    secondary: 'rgb(16, 24, 40)',
    card: 'rgba(16, 24, 40, 0.8)'
  },
  border: {
    primary: 'rgba(24, 144, 255, 0.2)',
    hover: 'rgba(24, 144, 255, 0.4)',
    active: 'rgba(24, 144, 255, 0.6)'
  },
  text: {
    primary: '#ffffff',
    secondary: 'rgba(255, 255, 255, 0.8)',
    muted: 'rgba(255, 255, 255, 0.6)'
  }
} as const;

// 数据大屏动画配置
export const DATA_SCREEN_ANIMATIONS = {
  duration: {
    fast: '0.2s',
    normal: '0.3s',
    slow: '0.5s'
  },
  easing: {
    ease: 'ease',
    easeInOut: 'ease-in-out',
    easeOut: 'ease-out'
  }
} as const;

// 响应式断点
export const DATA_SCREEN_BREAKPOINTS = {
  mobile: 768,
  tablet: 1024,
  desktop: 1366,
  large: 1920,
  ultrawide: 2560
} as const;

/**
 * 格式化数值显示
 * @param value 数值
 * @param options 格式化选项
 */
export function formatDataValue(
  value: number,
  options: {
    decimals?: number;
    unit?: string;
    compact?: boolean;
    locale?: string;
  } = {}
): string {
  const {
    decimals = 0,
    unit = '',
    compact = false,
    locale = 'zh-CN'
  } = options;

  let formattedValue: string;

  if (compact && value >= 1000) {
    const units = ['', 'K', 'M', 'B', 'T'];
    const unitIndex = Math.floor(Math.log10(value) / 3);
    const scaledValue = value / Math.pow(1000, unitIndex);
    formattedValue = scaledValue.toFixed(decimals) + units[unitIndex];
  } else {
    formattedValue = value.toLocaleString(locale, {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    });
  }

  return unit ? `${formattedValue}${unit}` : formattedValue;
}

/**
 * 格式化百分比
 * @param value 数值 (0-1 或 0-100)
 * @param isDecimal 是否为小数形式 (0-1)
 * @param decimals 小数位数
 */
export function formatPercentage(
  value: number,
  isDecimal = false,
  decimals = 1
): string {
  const percentage = isDecimal ? value * 100 : value;
  return `${percentage.toFixed(decimals)}%`;
}

/**
 * 获取状态颜色
 * @param status 状态值
 * @param type 颜色类型
 */
export function getStatusColor(
  status: 'success' | 'warning' | 'error' | 'normal',
  type: 'background' | 'border' | 'text' = 'background'
): string {
  const colorMap = {
    success: {
      background: DATA_SCREEN_COLORS.success,
      border: DATA_SCREEN_COLORS.success,
      text: DATA_SCREEN_COLORS.success
    },
    warning: {
      background: DATA_SCREEN_COLORS.warning,
      border: DATA_SCREEN_COLORS.warning,
      text: DATA_SCREEN_COLORS.warning
    },
    error: {
      background: DATA_SCREEN_COLORS.error,
      border: DATA_SCREEN_COLORS.error,
      text: DATA_SCREEN_COLORS.error
    },
    normal: {
      background: DATA_SCREEN_COLORS.primary,
      border: DATA_SCREEN_COLORS.primary,
      text: DATA_SCREEN_COLORS.primary
    }
  };

  return colorMap[status][type];
}

/**
 * 生成渐变背景样式
 * @param direction 渐变方向
 * @param colors 颜色数组
 */
export function generateGradient(
  direction: string = '135deg',
  colors: string[] = [
    DATA_SCREEN_COLORS.background.primary,
    DATA_SCREEN_COLORS.background.secondary,
    DATA_SCREEN_COLORS.background.primary
  ]
): string {
  return `linear-gradient(${direction}, ${colors.join(', ')})`;
}

/**
 * 生成发光效果样式
 * @param color 发光颜色
 * @param intensity 发光强度 (0-1)
 */
export function generateGlowEffect(
  color: string = DATA_SCREEN_COLORS.primary,
  intensity: number = 0.3
): string {
  const rgba = hexToRgba(color, intensity);
  return `0 0 20px ${rgba}, inset 0 0 20px ${hexToRgba(color, intensity * 0.3)}`;
}

/**
 * 将十六进制颜色转换为 RGBA
 * @param hex 十六进制颜色
 * @param alpha 透明度
 */
export function hexToRgba(hex: string, alpha: number = 1): string {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}

/**
 * 获取响应式类名
 * @param baseClass 基础类名
 * @param breakpoint 断点
 */
export function getResponsiveClass(
  baseClass: string,
  breakpoint: keyof typeof DATA_SCREEN_BREAKPOINTS
): string {
  const prefixMap = {
    mobile: 'sm:',
    tablet: 'md:',
    desktop: 'lg:',
    large: 'xl:',
    ultrawide: '2xl:'
  };
  
  return `${prefixMap[breakpoint]}${baseClass}`;
}

/**
 * 创建数据卡片样式对象
 * @param options 样式选项
 */
export function createCardStyle(options: {
  glow?: boolean;
  hover?: boolean;
  background?: string;
  border?: string;
} = {}): Record<string, string> {
  const {
    glow = false,
    hover = true,
    background = DATA_SCREEN_COLORS.background.card,
    border = DATA_SCREEN_COLORS.border.primary
  } = options;

  const style: Record<string, string> = {
    background,
    border: `1px solid ${border}`,
    borderRadius: '8px',
    backdropFilter: 'blur(10px)',
    transition: `all ${DATA_SCREEN_ANIMATIONS.duration.normal} ${DATA_SCREEN_ANIMATIONS.easing.ease}`
  };

  if (glow) {
    style.boxShadow = generateGlowEffect();
  }

  return style;
}

/**
 * 创建数据值样式对象
 * @param size 尺寸
 * @param color 颜色
 */
export function createValueStyle(
  size: 'small' | 'medium' | 'large' = 'medium',
  color: string = DATA_SCREEN_COLORS.primary
): Record<string, string> {
  const sizeMap = {
    small: '1.2rem',
    medium: '1.8rem',
    large: '2.5rem'
  };

  return {
    color,
    fontSize: sizeMap[size],
    fontWeight: 'bold',
    fontFamily: '"Courier New", monospace',
    textShadow: `0 0 15px ${hexToRgba(color, 0.6)}`
  };
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param delay 延迟时间
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(null, args), delay);
  };
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param delay 延迟时间
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func.apply(null, args);
    }
  };
}

/**
 * 生成随机数据（用于演示）
 * @param min 最小值
 * @param max 最大值
 * @param decimals 小数位数
 */
export function generateRandomData(
  min: number = 0,
  max: number = 100,
  decimals: number = 0
): number {
  const random = Math.random() * (max - min) + min;
  return Number(random.toFixed(decimals));
}

/**
 * 创建时间序列数据
 * @param count 数据点数量
 * @param interval 时间间隔（分钟）
 */
export function createTimeSeriesData(
  count: number = 24,
  interval: number = 60
): Array<{ time: string; value: number }> {
  const data = [];
  const now = new Date();
  
  for (let i = count - 1; i >= 0; i--) {
    const time = new Date(now.getTime() - i * interval * 60 * 1000);
    const value = generateRandomData(20, 100, 1);
    
    data.push({
      time: time.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      }),
      value
    });
  }
  
  return data;
}

// 导出类型定义
export type DataScreenColor = typeof DATA_SCREEN_COLORS;
export type StatusType = 'success' | 'warning' | 'error' | 'normal';
export type SizeType = 'small' | 'medium' | 'large';
export type BreakpointType = keyof typeof DATA_SCREEN_BREAKPOINTS;
