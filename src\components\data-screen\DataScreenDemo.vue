<template>
  <div class="data-screen-container">
    <!-- 网格背景 -->
    <div class="data-screen-grid"></div>
    
    <!-- 主要内容区域 -->
    <div class="p-6 relative z-10">
      <!-- 标题区域 -->
      <div class="text-center mb-8">
        <h1 class="data-screen-title text-4xl mb-2">数据监控大屏</h1>
        <p class="data-screen-subtitle text-lg">实时数据展示与分析平台</p>
      </div>
      
      <!-- 数据卡片网格 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- 在线用户数 -->
        <div class="data-card p-6 glow-animation">
          <div class="flex items-center justify-between mb-4">
            <h3 class="data-screen-subtitle text-sm">在线用户</h3>
            <span class="status-indicator status-online"></span>
          </div>
          <div class="data-value data-value-large">{{ onlineUsers.toLocaleString() }}</div>
          <div class="text-xs text-gray-400 mt-2">
            <span class="text-green-400">↗ +12.5%</span> 较昨日
          </div>
        </div>
        
        <!-- 总访问量 -->
        <div class="data-card p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="data-screen-subtitle text-sm">总访问量</h3>
            <span class="status-indicator status-online"></span>
          </div>
          <div class="data-value data-value-large">{{ totalVisits.toLocaleString() }}</div>
          <div class="text-xs text-gray-400 mt-2">
            <span class="text-green-400">↗ +8.3%</span> 较昨日
          </div>
        </div>
        
        <!-- 系统负载 -->
        <div class="data-card p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="data-screen-subtitle text-sm">系统负载</h3>
            <span class="status-indicator status-warning"></span>
          </div>
          <div class="data-value data-value-large">{{ systemLoad }}%</div>
          <div class="text-xs text-gray-400 mt-2">
            <span class="text-yellow-400">↗ +2.1%</span> 较昨日
          </div>
        </div>
        
        <!-- 错误率 -->
        <div class="data-card p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="data-screen-subtitle text-sm">错误率</h3>
            <span class="status-indicator status-online"></span>
          </div>
          <div class="data-value data-value-large">{{ errorRate }}%</div>
          <div class="text-xs text-gray-400 mt-2">
            <span class="text-green-400">↘ -0.5%</span> 较昨日
          </div>
        </div>
      </div>
      
      <!-- 图表区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- 访问趋势图 -->
        <div class="chart-container">
          <h3 class="data-screen-title text-lg mb-4">访问趋势</h3>
          <div class="h-64 flex items-center justify-center">
            <div class="text-center">
              <div class="data-value data-value-medium mb-2">图表区域</div>
              <p class="data-screen-subtitle text-sm">这里可以集成 ECharts 或其他图表库</p>
            </div>
          </div>
        </div>
        
        <!-- 地域分布 -->
        <div class="chart-container">
          <h3 class="data-screen-title text-lg mb-4">地域分布</h3>
          <div class="h-64 flex items-center justify-center">
            <div class="text-center">
              <div class="data-value data-value-medium mb-2">地图区域</div>
              <p class="data-screen-subtitle text-sm">这里可以集成地图组件</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 实时数据表格 -->
      <div class="data-card p-6">
        <h3 class="data-screen-title text-lg mb-4">实时监控数据</h3>
        <div class="overflow-x-auto">
          <table class="data-table w-full">
            <thead>
              <tr>
                <th class="px-4 py-3 text-left">服务名称</th>
                <th class="px-4 py-3 text-left">状态</th>
                <th class="px-4 py-3 text-left">响应时间</th>
                <th class="px-4 py-3 text-left">CPU使用率</th>
                <th class="px-4 py-3 text-left">内存使用率</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="service in services" :key="service.name">
                <td class="px-4 py-3">{{ service.name }}</td>
                <td class="px-4 py-3">
                  <span :class="['status-indicator', getStatusClass(service.status)]"></span>
                  {{ service.status }}
                </td>
                <td class="px-4 py-3">
                  <span class="data-value data-value-small">{{ service.responseTime }}ms</span>
                </td>
                <td class="px-4 py-3">
                  <div class="flex items-center">
                    <div class="progress-bar w-20 h-2 mr-2">
                      <div class="progress-fill" :style="{ width: service.cpu + '%' }"></div>
                    </div>
                    <span class="data-value data-value-small">{{ service.cpu }}%</span>
                  </div>
                </td>
                <td class="px-4 py-3">
                  <div class="flex items-center">
                    <div class="progress-bar w-20 h-2 mr-2">
                      <div class="progress-fill" :style="{ width: service.memory + '%' }"></div>
                    </div>
                    <span class="data-value data-value-small">{{ service.memory }}%</span>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';

defineOptions({
  name: 'DataScreenDemo'
});

// 响应式数据
const onlineUsers = ref(12580);
const totalVisits = ref(1258000);
const systemLoad = ref(68);
const errorRate = ref(0.12);

const services = ref([
  { name: 'Web服务', status: '正常', responseTime: 45, cpu: 35, memory: 42 },
  { name: '数据库', status: '正常', responseTime: 12, cpu: 28, memory: 65 },
  { name: 'Redis缓存', status: '正常', responseTime: 3, cpu: 15, memory: 38 },
  { name: 'API网关', status: '警告', responseTime: 89, cpu: 72, memory: 55 },
  { name: '消息队列', status: '正常', responseTime: 23, cpu: 41, memory: 33 }
]);

// 状态样式映射
const getStatusClass = (status: string) => {
  const statusMap: Record<string, string> = {
    '正常': 'status-online',
    '警告': 'status-warning',
    '错误': 'status-error'
  };
  return statusMap[status] || 'status-online';
};

// 模拟实时数据更新
let updateInterval: NodeJS.Timeout;

const updateData = () => {
  // 模拟数据变化
  onlineUsers.value += Math.floor(Math.random() * 20 - 10);
  totalVisits.value += Math.floor(Math.random() * 100);
  systemLoad.value = Math.max(0, Math.min(100, systemLoad.value + Math.random() * 4 - 2));
  errorRate.value = Math.max(0, errorRate.value + (Math.random() * 0.02 - 0.01));
  
  // 更新服务数据
  services.value.forEach(service => {
    service.responseTime += Math.floor(Math.random() * 10 - 5);
    service.cpu = Math.max(0, Math.min(100, service.cpu + Math.random() * 6 - 3));
    service.memory = Math.max(0, Math.min(100, service.memory + Math.random() * 4 - 2));
    
    // 随机改变状态
    if (Math.random() < 0.05) {
      const statuses = ['正常', '警告', '错误'];
      service.status = statuses[Math.floor(Math.random() * statuses.length)];
    }
  });
};

onMounted(() => {
  // 每3秒更新一次数据
  updateInterval = setInterval(updateData, 3000);
});

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval);
  }
});
</script>

<style scoped>
/* 组件特定样式 */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

.gap-6 {
  gap: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.p-6 {
  padding: 1.5rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.h-64 {
  height: 16rem;
}

.h-2 {
  height: 0.5rem;
}

.w-full {
  width: 100%;
}

.w-20 {
  width: 5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.overflow-x-auto {
  overflow-x: auto;
}

.relative {
  position: relative;
}

.z-10 {
  z-index: 10;
}

.text-gray-400 {
  color: rgb(156 163 175);
}

.text-green-400 {
  color: rgb(74 222 128);
}

.text-yellow-400 {
  color: rgb(250 204 21);
}
</style>
